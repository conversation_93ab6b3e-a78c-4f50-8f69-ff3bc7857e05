请帮我生成一个可以在本地运行的抖音小程序支付示例项目，要求如下：

1. 业务场景：用户点击按钮支付 1 元，使用抖音小程序新版支付接口 tt.requestOrder。
2. 包含完整的前端代码（index.ttml + index.js），前端点击按钮调用后端接口获取订单数据，再调用 tt.requestOrder 发起支付。
3. 包含完整的 Node.js 后端示例，使用 Express 框架，负责调用抖音开放平台创建订单接口（https://open.douyin.com/api/apps/ecpay/v1/create_order）。
4. 后端需实现签名逻辑（SHA256），签名规则请根据抖音支付文档实现，参数完整。
5. 后端接口接收前端传来的支付金额和商品描述，返回前端需要的 order_info 和 byte_authorization。
6. 支付金额单位为分，示例为 100（即 1 元人民币）。
7. 前端支付成功后打印成功日志，失败时打印失败日志。
8. 后端提供支付回调接口示例，能打印回调参数用于调试。
9. 项目结构清晰，代码注释详尽，方便理解和复用。
10. 代码中需要标注哪些地方需要替换成自己的抖音小程序 appid、secret、商户号等配置。
11. 代码示例中包含如何安装依赖和启动项目的说明。

请生成符合以上需求的完整代码示例，便于我直接复制运行和后续二次开发。


1. 创建支付订单接口文档（新版）：https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/api/industry/general_trade/create_order/request-order
2. tt.pay 前端 API 文档：  https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/api/open-interface/pay/tt-pay

3:生成下单参数与签名:https://developer.open-douyin.com/docs/resource/zh-CN/mini-app/develop/server/payment/trade-system/general/order/request-order-data-sign