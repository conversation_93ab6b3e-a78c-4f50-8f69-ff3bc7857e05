tt.requestOrder
更新时间 2025-04-23 17:09:20


基础库 2.74.0 开始支持本方法，这是一个异步方法。

提供预下单能力，开发者通过调用该方法生成订单，返回订单号和订单信息。


前提条件	无
业务背景	无
使用限制	
最低支持版本上注明行业 SDK ，表示仅在行业 SDK 上才支持，需要在代码中配置行业 SDK 的权限：行业 SDK 的权限配置。当完成行业 SDK 的权限配置后，可通过 tt.canIUse('createOrder') 判断该 API 是否可用。

注意事项	无
支持沙盒	
否
相关教程	无
语法
tt.requestOrder(options)
参数说明
options 为 object 类型，属性如下：

属性名	类型	默认值	必填	说明	最低支持版本
data	string	无	是	
由开发者服务端返回，生成方式见此文档

2.74.0
byteAuthorization	string	无	是	
由开发者服务端返回，生成方式见此文档

2.74.0
success	function		否	
接口调用成功的回调函数
2.74.0
fail	function		否	
接口调用失败的回调函数
2.74.0
complete	function		否	
接口调用结束的回调函数（调用成功、失败都会执行）
2.74.0
回调成功
object 类型，属性如下：

属性名	类型	说明	最低支持版本
orderId	string	
抖音开放平台内部的交易订单号，拉起收银台的参数，示例：motb123456789

2.74.0
itemOrderList	object	
item单信息

2.74.0
logId	string	
可供服务端排查问题

2.74.0
itemOrderList 类型说明
object 类型，属性如下：

属性名	类型	说明	最低支持版本
itemOrderId	string	
交易系统商品单号，示例：motb874637654774

2.74.0
skuId	string	
商品id，开发者下单时传入的商品id

2.74.0
itemOrderAmount	number	
item单实付金额

2.74.0
回调失败
object 类型，属性如下：

属性名	类型	说明	最低支持版本
errNo	string	
错误码，对应信息可查看 errNo 说明

2.74.0
errMsg	string	
错误信息提示

2.74.0
errLogId	string	
当下单失败时会提供该数据，可供服务端排查问题

2.74.0
错误码
errNo	errMsg	说明	最低支持版本
10000	requestOrder:fail 参数错误	
参数错误

2.74.0
10000	requestOrder:fail 商品不符合行业要求	
商品类型与小程序所属行业不符。请仔细阅读接入规范，查看所属行业可交易的商品类型后修正参数

2.74.0
10401	requestOrder:fail internal error	
请求异常，可重试或升级APP

2.74.0
11004	requestOrder:fail 签名参数异常	
请检查byteAuthorization，若不是'SHA256-RSA2048 '开头的格式，请使用下方示例代码生成。

2.74.0
11004	requestOrder:fail 签名校验异常	
请参考加签问题FAQ进行自查

2.74.0
11001	requestOrder:fail 访问未授权	
该JSAPI依赖用户登录，使用前请确保用户已经完成登录

2.74.0
12002	requestOrder:fail 账号行为异常	
账号行为异常

2.74.0
13000	requestOrder:fail 系统错误	
系统错误；

可能情况较多，建议重试；若还重复报错，可以拉 Oncall 排查具体原因。

2.74.0
21016	requestOrder:fail 外部单号已存在	
外部单号已存在

2.74.0
21046	requestOrder:fail 订单收款商户号不合法	
订单收款商户号不合法

2.74.0
21550	requestOrder:fail not login	
请用真机调试或者参考常见问题

2.74.0
26003	requestOrder:fail 相关接口已被封禁,具体原因可进入「控制台-小程序-健康-违规记录」进行查询	
相关接口已被封禁,具体原因可进入「控制台-小程序-健康-违规记录」进行查询

2.74.0
26003	requestOrder:fail 无下单权限	
当前场景无下单权限，具体可咨询对接运营

2.74.0
26005	requestOrder:fail 无可用支付方式	
无可用支付方式

2.74.0
26006		 requestOrder:fail 商户号与小程序的支付产品不一致	
商户号与小程序的支付产品不一致

2.74.0
代码示例
// index.js
Page({
  data: {},
  createOrder() {
    tt.requestOrder({
      data: JSON.stringify({
        orderEntrySchema: {
          path: "page/index/index", // 小程序详情页跳转路径
          params: '{"id":1234, "name":"hello"}', // 详情页路径参数
        },
        skuList: [
          {
            tagGroupId: "test",
            skuId: "abcd",
            title: "test",
            price: 1,
            imageList: ["https://example.com/test.png"],
            type: 101,
            quantity: 1,
          },
        ], // 下单商品信息
        outOrderNo: "out_order_test_123456", // 外部订单号
        totalAmount: 1, // 订单总金额
        payExpireSeconds: 300, // 支付超时时间
        limitPayWayList: [], // 屏蔽的支付方式
      }), // 开发者服务端返回的data示例
      byteAuthorization: 'test authorization', // 开发者服务端返回的byteAuthorization
      success: (res) => {
        const { orderId } = res;
        console.log("orderId", orderId); // 抖音开放平台内部的交易订单号
      },
      fail: (res) => {
        const { errLogId, errMsg, errNo } = res;
        console.log("errNo:", errNo);
        console.log("errMsg:", errMsg);
      },
    });
  }
});